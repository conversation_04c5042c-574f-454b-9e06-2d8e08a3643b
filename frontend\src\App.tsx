import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import ReportFound from './pages/ReportFound';
import SearchLost from './pages/SearchLost';
import MyReports from './pages/MyReports';
import Login from './pages/Login';
import Register from './pages/Register';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <main className="container mx-auto px-4 py-8">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/report-found" element={<ReportFound />} />
              <Route path="/search-lost" element={<SearchLost />} />
              <Route path="/my-reports" element={<MyReports />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
            </Routes>
          </main>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
