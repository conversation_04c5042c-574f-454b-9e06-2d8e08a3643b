import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Navigate } from 'react-router-dom';

const MyReports: React.FC = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div className="text-center py-8">Loading...</div>;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">My Reports</h1>
      <div className="bg-white rounded-lg shadow-lg p-8">
        <p className="text-gray-600 text-center py-8">
          My Reports dashboard will be implemented here.
        </p>
      </div>
    </div>
  );
};

export default MyReports;
