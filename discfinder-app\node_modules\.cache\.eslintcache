[{"C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts": "4", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx": "5", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx": "6", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx": "7", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AdminBulkTurnins.tsx": "8", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\RakerDiverDashboard.tsx": "9", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileManager.tsx": "10", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileImportManager.tsx": "11", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AvatarUpload.tsx": "12", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\profileImport.ts": "13", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\PhotoMigrationManager.tsx": "14", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\photoMigration.ts": "15", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\SourceManager.tsx": "16", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ContactAttempts.tsx": "17", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\smsService.ts": "18", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\utils\\phoneUtils.ts": "19"}, {"size": 554, "mtime": 1751073000293, "results": "20", "hashOfConfig": "21"}, {"size": 425, "mtime": 1751073000018, "results": "22", "hashOfConfig": "21"}, {"size": 40673, "mtime": 1751598962945, "results": "23", "hashOfConfig": "21"}, {"size": 25133, "mtime": 1751567322723, "results": "24", "hashOfConfig": "21"}, {"size": 13930, "mtime": 1751599280886, "results": "25", "hashOfConfig": "21"}, {"size": 6258, "mtime": 1751083217163, "results": "26", "hashOfConfig": "21"}, {"size": 4373, "mtime": 1751086289968, "results": "27", "hashOfConfig": "21"}, {"size": 17718, "mtime": 1751470528232, "results": "28", "hashOfConfig": "21"}, {"size": 15901, "mtime": 1751470556348, "results": "29", "hashOfConfig": "21"}, {"size": 12110, "mtime": 1751564744466, "results": "30", "hashOfConfig": "21"}, {"size": 8656, "mtime": 1751121281468, "results": "31", "hashOfConfig": "21"}, {"size": 6035, "mtime": 1751121314460, "results": "32", "hashOfConfig": "21"}, {"size": 6808, "mtime": 1751121248928, "results": "33", "hashOfConfig": "21"}, {"size": 9524, "mtime": 1751289718752, "results": "34", "hashOfConfig": "21"}, {"size": 9028, "mtime": 1751290059629, "results": "35", "hashOfConfig": "21"}, {"size": 12114, "mtime": 1751294455192, "results": "36", "hashOfConfig": "21"}, {"size": 12620, "mtime": 1751305960447, "results": "37", "hashOfConfig": "21"}, {"size": 4339, "mtime": 1751567353387, "results": "38", "hashOfConfig": "21"}, {"size": 4199, "mtime": 1751470501013, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ncyc5o", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx", ["97"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AdminBulkTurnins.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\RakerDiverDashboard.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileManager.tsx", [], ["98"], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ProfileImportManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\AvatarUpload.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\profileImport.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\PhotoMigrationManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\photoMigration.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\SourceManager.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ContactAttempts.tsx", [], ["99"], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\smsService.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\utils\\phoneUtils.ts", [], [], {"ruleId": "100", "severity": 1, "message": "101", "line": 122, "column": 6, "nodeType": "102", "endLine": 122, "endColumn": 8, "suggestions": "103"}, {"ruleId": "100", "severity": 1, "message": "104", "line": 34, "column": 6, "nodeType": "102", "endLine": 34, "endColumn": 14, "suggestions": "105", "suppressions": "106"}, {"ruleId": "100", "severity": 1, "message": "107", "line": 18, "column": 6, "nodeType": "102", "endLine": 18, "endColumn": 14, "suggestions": "108", "suppressions": "109"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["110"], "React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array.", ["111"], ["112"], "React Hook useEffect has a missing dependency: 'loadContactAttempts'. Either include it or remove the dependency array.", ["113"], ["114"], {"desc": "115", "fix": "116"}, {"desc": "117", "fix": "118"}, {"kind": "119", "justification": "120"}, {"desc": "121", "fix": "122"}, {"kind": "119", "justification": "120"}, "Update the dependencies array to be: [fetchProfile]", {"range": "123", "text": "124"}, "Update the dependencies array to be: [loadProfile, userId]", {"range": "125", "text": "126"}, "directive", "", "Update the dependencies array to be: [discId, loadContactAttempts]", {"range": "127", "text": "128"}, [3756, 3758], "[fetchProfile]", [1094, 1102], "[loadProfile, userId]", [647, 655], "[discId, loadContactAttempts]"]