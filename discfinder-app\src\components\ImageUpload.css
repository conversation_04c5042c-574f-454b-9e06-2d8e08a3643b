.image-upload {
  margin-bottom: 1.5rem;
}

.image-upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.image-upload-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.image-upload-info {
  font-size: 0.75rem;
  color: #6b7280;
}

.image-upload-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.existing-images {
  margin-bottom: 1rem;
}

.existing-images h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.image-previews {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.image-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid #e5e7eb;
  background-color: #f9fafb;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem;
  text-align: center;
}

.remove-image {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  transition: background-color 0.2s;
}

.remove-image:hover {
  background-color: rgba(220, 38, 38, 1);
}

.remove-image:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.image-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fafafa;
}

.image-upload-area:hover {
  border-color: #3b82f6;
  background-color: #f8faff;
}

.image-upload-area.drag-active {
  border-color: #3b82f6;
  background-color: #eff6ff;
  transform: scale(1.02);
}

.image-upload-area.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f3f4f6;
}

.image-upload-area.disabled:hover {
  border-color: #d1d5db;
  background-color: #f3f4f6;
  transform: none;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 3rem;
  opacity: 0.6;
}

.upload-text p {
  margin: 0;
  color: #374151;
}

.upload-text p:first-child {
  font-weight: 500;
  font-size: 1rem;
}

.upload-subtext {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Responsive design */
@media (max-width: 640px) {
  .image-upload-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .image-previews {
    gap: 0.5rem;
  }
  
  .image-preview {
    width: 100px;
    height: 100px;
  }
  
  .image-upload-area {
    padding: 1.5rem;
  }
  
  .upload-icon {
    font-size: 2rem;
  }
}
