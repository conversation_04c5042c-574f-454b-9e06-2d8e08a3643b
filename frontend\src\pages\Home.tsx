import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Home: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="max-w-4xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-12">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Lost Your Disc?
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          DiscFinder helps disc golf players reunite with their lost discs. 
          Report found discs or search for your lost ones in our community database.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/report-found"
            className="bg-primary-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-primary-700 transition-colors"
          >
            Report Found Disc
          </Link>
          <Link
            to="/search-lost"
            className="bg-white text-primary-600 border-2 border-primary-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-primary-50 transition-colors"
          >
            Search Found Discs
          </Link>
        </div>
      </div>

      {/* Features Section */}
      <div className="grid md:grid-cols-3 gap-8 py-12">
        <div className="text-center">
          <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <div className="w-8 h-8 text-primary-600 text-2xl">🔍</div>
          </div>
          <h3 className="text-xl font-semibold mb-2">Smart Matching</h3>
          <p className="text-gray-600">
            Our intelligent system matches found and lost discs based on brand, model, color, and location.
          </p>
        </div>

        <div className="text-center">
          <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <div className="w-8 h-8 text-primary-600 text-2xl">📍</div>
          </div>
          <h3 className="text-xl font-semibold mb-2">Location Based</h3>
          <p className="text-gray-600">
            Find discs near where you lost them with our location-based search and matching.
          </p>
        </div>

        <div className="text-center">
          <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <div className="w-8 h-8 text-primary-600 text-2xl">💬</div>
          </div>
          <h3 className="text-xl font-semibold mb-2">Easy Communication</h3>
          <p className="text-gray-600">
            Connect directly with finders and owners through our secure messaging system.
          </p>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white rounded-lg shadow-lg p-8 my-12">
        <div className="grid md:grid-cols-3 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-primary-600 mb-2">500+</div>
            <div className="text-gray-600">Discs Reunited</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary-600 mb-2">1,200+</div>
            <div className="text-gray-600">Active Users</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary-600 mb-2">95%</div>
            <div className="text-gray-600">Success Rate</div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      {!user && (
        <div className="bg-primary-600 text-white rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Join the Community</h2>
          <p className="text-primary-100 mb-6">
            Create an account to report found discs, search for lost ones, and help fellow disc golfers.
          </p>
          <Link
            to="/register"
            className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Sign Up Now
          </Link>
        </div>
      )}
    </div>
  );
};

export default Home;
