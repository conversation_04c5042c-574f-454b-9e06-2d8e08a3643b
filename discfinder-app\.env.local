# Supabase Configuration
# Replace these with your actual Supabase project credentials
# Get these from: https://app.supabase.com/project/YOUR_PROJECT/settings/api

REACT_APP_SUPABASE_URL=https://plrnyfpxfbwdexbjzqzw.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.re78ar2XicymH_eJLjFR6RZ4LVuuk8PIR7d2G9Onq6s

# Service Role Key (needed for profile import)
# Get this from: https://app.supabase.com/project/plrnyfpxfbwdexbjzqzw/settings/api
# Look for "service_role" key (NOT the anon key above)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.8f5aaXZCYTVwpCeKvkJjVN4svHl5x72Tv3GjOK4VEFk

# Twilio Configuration (for SMS notifications)
# Get these from: https://console.twilio.com/
# Account SID and Auth Token from your Twilio Console
REACT_APP_TWILIO_ACCOUNT_SID=**********************************
REACT_APP_TWILIO_AUTH_TOKEN=704e7ee4c5b5ec4b10d2ba15762ec7f0
REACT_APP_TWILIO_PHONE_NUMBER=+***********
