{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [profile, setProfile] = useState(null);\n  const [session, setSession] = useState(null);\n  const [userRole, setUserRole] = useState(() => {\n    // Try to restore role from localStorage on initial load\n    try {\n      const savedRole = localStorage.getItem('discfinder_user_role');\n      return savedRole || 'guest';\n    } catch {\n      return 'guest';\n    }\n  });\n  const [loading, setLoading] = useState(true);\n\n  // Helper function to update user role and persist to localStorage\n  const updateUserRole = newRole => {\n    setUserRole(newRole);\n    try {\n      if (newRole === 'guest') {\n        localStorage.removeItem('discfinder_user_role');\n      } else {\n        localStorage.setItem('discfinder_user_role', newRole);\n      }\n    } catch (error) {\n      console.warn('Failed to save user role to localStorage:', error);\n    }\n  };\n  useEffect(() => {\n    // Get initial session with timeout\n    const initAuth = async () => {\n      try {\n        var _session$user;\n        console.log('Initializing auth...');\n        const {\n          data: {\n            session\n          },\n          error\n        } = await supabase.auth.getSession();\n        if (error) {\n          console.warn('Auth session error:', error);\n          updateUserRole('guest');\n          setLoading(false);\n          return;\n        }\n        setSession(session);\n        setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n        if (session !== null && session !== void 0 && session.user) {\n          console.log('User found, fetching profile...');\n          await fetchProfile(session.user.id);\n        } else {\n          console.log('No user session, setting as guest');\n          updateUserRole('guest');\n          setLoading(false);\n        }\n      } catch (error) {\n        console.warn('Auth initialization failed:', error);\n        updateUserRole('guest');\n        setLoading(false);\n      }\n    };\n\n    // Set a timeout to prevent infinite loading\n    const timeoutId = setTimeout(() => {\n      console.warn('Auth initialization timeout, setting as guest');\n      updateUserRole('guest');\n      setLoading(false);\n    }, 5000); // 5 second timeout\n\n    initAuth().finally(() => {\n      clearTimeout(timeoutId);\n    });\n\n    // Listen for auth changes\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      var _session$user2;\n      setSession(session);\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n      if (session !== null && session !== void 0 && session.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n        updateUserRole('guest');\n        setLoading(false);\n      }\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const fetchProfile = async (userId, retryCount = 0) => {\n    try {\n      console.log('Fetching profile for user:', userId, retryCount > 0 ? `(retry ${retryCount})` : '');\n\n      // Add timeout for profile fetch\n      const profilePromise = supabase.from('profiles').select('*').eq('id', userId).single();\n      const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Profile fetch timeout')), 5000) // Increased timeout\n      );\n      const {\n        data,\n        error\n      } = await Promise.race([profilePromise, timeoutPromise]);\n      if (error) {\n        console.warn('Profile fetch error:', error);\n\n        // If profile doesn't exist, check for imported profile or create new one\n        if (error.code === 'PGRST116') {\n          console.log('Profile not found, checking for imported profile...');\n          try {\n            const {\n              data: userData\n            } = await supabase.auth.getUser();\n            if (userData.user) {\n              var _userData$user$user_m2;\n              // First, check if there's an imported profile in staging with this email\n              const {\n                data: importedProfile,\n                error: importError\n              } = await supabase.from('imported_profiles_staging').select('*').eq('email', userData.user.email).single();\n              if (importedProfile && !importError) {\n                var _userData$user$user_m;\n                console.log('Found imported profile, creating linked profile...');\n                // Create profile using imported data\n                const newProfile = {\n                  id: userData.user.id,\n                  email: userData.user.email,\n                  full_name: ((_userData$user$user_m = userData.user.user_metadata) === null || _userData$user$user_m === void 0 ? void 0 : _userData$user$user_m.full_name) || importedProfile.full_name,\n                  role: importedProfile.role,\n                  legacy_row_id: importedProfile.legacy_row_id,\n                  pdga_number: importedProfile.pdga_number,\n                  facebook_profile: importedProfile.facebook_profile,\n                  instagram_handle: importedProfile.instagram_handle,\n                  sms_number: importedProfile.sms_number,\n                  phone_number: importedProfile.phone_number,\n                  avatar_url: importedProfile.avatar_url,\n                  created_at: new Date().toISOString(),\n                  updated_at: new Date().toISOString()\n                };\n                const {\n                  data: createdProfile,\n                  error: createError\n                } = await supabase.from('profiles').insert([newProfile]).select().single();\n                if (createError) {\n                  console.error('Error creating linked profile:', createError);\n                } else {\n                  console.log('Successfully created linked profile');\n\n                  // Remove from staging since it's now linked\n                  await supabase.from('imported_profiles_staging').delete().eq('id', importedProfile.id);\n                  setProfile(createdProfile);\n                  updateUserRole(createdProfile.role || 'user');\n                  setLoading(false);\n                  return;\n                }\n              }\n\n              // No imported profile found, create a new one\n              console.log('No imported profile found, creating new profile...');\n              const newProfile = {\n                id: userData.user.id,\n                email: userData.user.email,\n                full_name: ((_userData$user$user_m2 = userData.user.user_metadata) === null || _userData$user$user_m2 === void 0 ? void 0 : _userData$user$user_m2.full_name) || '',\n                role: userData.user.email === '<EMAIL>' ? 'admin' : 'user',\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n              };\n              const {\n                data: createdProfile,\n                error: createError\n              } = await supabase.from('profiles').insert([newProfile]).select().single();\n              if (createError) {\n                console.error('Error creating new profile:', createError);\n              } else {\n                console.log('Profile created successfully');\n                setProfile(createdProfile);\n                updateUserRole(createdProfile.role || 'user');\n                setLoading(false);\n                return;\n              }\n            }\n          } catch (createError) {\n            console.warn('Failed to create profile:', createError);\n          }\n        }\n\n        // If we can't fetch/create profile, preserve existing role if we have one\n        console.log('Continuing without profile data, preserving existing role:', userRole);\n        if (userRole === 'guest') {\n          updateUserRole('user');\n        }\n        // Otherwise keep the existing role to prevent losing admin access\n        setLoading(false);\n        return;\n      }\n      console.log('Profile fetched successfully:', data);\n      setProfile(data);\n      setUserRole(data.role || 'user');\n      setLoading(false);\n    } catch (error) {\n      var _message;\n      console.error('Error in fetchProfile:', error);\n\n      // Retry on network errors (but not more than 2 times)\n      if (retryCount < 2 && error !== null && error !== void 0 && (_message = error.message) !== null && _message !== void 0 && _message.includes('timeout')) {\n        console.log('Retrying profile fetch due to timeout...');\n        setTimeout(() => fetchProfile(userId, retryCount + 1), 1000 * (retryCount + 1));\n        return;\n      }\n\n      // Don't fail completely, preserve existing role to prevent losing admin access\n      console.log('Preserving existing role due to fetch error:', userRole);\n      if (userRole === 'guest') {\n        setUserRole('user');\n      }\n      // Otherwise keep the existing role\n      setLoading(false);\n    }\n  };\n  const signUp = async (email, password, fullName) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName\n          }\n        }\n      });\n\n      // If user creation succeeded, check for imported profile or create new one\n      if (data.user && !error) {\n        try {\n          // Look for an existing imported profile in staging with this email\n          const {\n            data: importedProfile,\n            error: importError\n          } = await supabase.from('imported_profiles_staging').select('*').eq('email', data.user.email).single();\n          if (importedProfile && !importError) {\n            console.log('Found imported profile during signup, creating linked profile...');\n            // Create profile using imported data\n            await supabase.from('profiles').insert([{\n              id: data.user.id,\n              email: data.user.email,\n              full_name: fullName || importedProfile.full_name,\n              role: importedProfile.role,\n              legacy_row_id: importedProfile.legacy_row_id,\n              pdga_number: importedProfile.pdga_number,\n              facebook_profile: importedProfile.facebook_profile,\n              instagram_handle: importedProfile.instagram_handle,\n              sms_number: importedProfile.sms_number,\n              phone_number: importedProfile.phone_number,\n              avatar_url: importedProfile.avatar_url,\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString()\n            }]);\n\n            // Remove from staging since it's now linked\n            await supabase.from('imported_profiles_staging').delete().eq('id', importedProfile.id);\n            console.log('Successfully linked imported profile during signup');\n          } else {\n            // No imported profile, create new one\n            await supabase.from('profiles').insert([{\n              id: data.user.id,\n              email: data.user.email,\n              full_name: fullName,\n              role: data.user.email === '<EMAIL>' ? 'admin' : 'user',\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString()\n            }]);\n          }\n        } catch (profileError) {\n          console.warn('Profile creation/linking failed, but user was created:', profileError);\n          // Don't fail the signup if profile creation fails\n        }\n      }\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signIn = async (email, password) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signOut = async () => {\n    try {\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n  const updateProfile = async updates => {\n    if (!user) return;\n    try {\n      const {\n        error\n      } = await supabase.from('profiles').update(updates).eq('id', user.id);\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n  const value = {\n    user,\n    profile,\n    session,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    isGuest: userRole === 'guest',\n    isUser: userRole === 'user',\n    isAdmin: userRole === 'admin',\n    isRakerDiver: userRole === 'rakerdiver'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 403,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"BcRJMQFzVZxLVuYDyjg63rE387o=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "supabase", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "profile", "setProfile", "session", "setSession", "userRole", "setUserRole", "savedRole", "localStorage", "getItem", "loading", "setLoading", "updateUserRole", "newRole", "removeItem", "setItem", "error", "console", "warn", "initAuth", "_session$user", "log", "data", "auth", "getSession", "fetchProfile", "id", "timeoutId", "setTimeout", "finally", "clearTimeout", "subscription", "onAuthStateChange", "event", "_session$user2", "unsubscribe", "userId", "retryCount", "profilePromise", "from", "select", "eq", "single", "timeoutPromise", "Promise", "_", "reject", "race", "code", "userData", "getUser", "_userData$user$user_m2", "importedProfile", "importError", "email", "_userData$user$user_m", "newProfile", "full_name", "user_metadata", "role", "legacy_row_id", "pdga_number", "facebook_profile", "instagram_handle", "sms_number", "phone_number", "avatar_url", "created_at", "Date", "toISOString", "updated_at", "createdProfile", "createError", "insert", "delete", "_message", "message", "includes", "signUp", "password", "fullName", "options", "profileError", "signIn", "signInWithPassword", "signOut", "updateProfile", "updates", "update", "value", "isGuest", "isUser", "isAdmin", "isRakerDiver", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase, Profile } from '../lib/supabase';\n\nexport type UserRole = 'guest' | 'user' | 'admin' | 'rakerdiver';\n\ninterface AuthContextType {\n  user: User | null;\n  profile: Profile | null;\n  session: Session | null;\n  userRole: UserRole;\n  loading: boolean;\n  signUp: (email: string, password: string, fullName: string) => Promise<any>;\n  signIn: (email: string, password: string) => Promise<any>;\n  signOut: () => Promise<void>;\n  updateProfile: (updates: Partial<Profile>) => Promise<void>;\n  isGuest: boolean;\n  isUser: boolean;\n  isAdmin: boolean;\n  isRakerDiver: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [profile, setProfile] = useState<Profile | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [userRole, setUserRole] = useState<UserRole>(() => {\n    // Try to restore role from localStorage on initial load\n    try {\n      const savedRole = localStorage.getItem('discfinder_user_role');\n      return (savedRole as UserRole) || 'guest';\n    } catch {\n      return 'guest';\n    }\n  });\n  const [loading, setLoading] = useState(true);\n\n  // Helper function to update user role and persist to localStorage\n  const updateUserRole = (newRole: UserRole) => {\n    setUserRole(newRole);\n    try {\n      if (newRole === 'guest') {\n        localStorage.removeItem('discfinder_user_role');\n      } else {\n        localStorage.setItem('discfinder_user_role', newRole);\n      }\n    } catch (error) {\n      console.warn('Failed to save user role to localStorage:', error);\n    }\n  };\n\n  useEffect(() => {\n    // Get initial session with timeout\n    const initAuth = async () => {\n      try {\n        console.log('Initializing auth...');\n        const { data: { session }, error } = await supabase.auth.getSession();\n\n        if (error) {\n          console.warn('Auth session error:', error);\n          updateUserRole('guest');\n          setLoading(false);\n          return;\n        }\n\n        setSession(session);\n        setUser(session?.user ?? null);\n\n        if (session?.user) {\n          console.log('User found, fetching profile...');\n          await fetchProfile(session.user.id);\n        } else {\n          console.log('No user session, setting as guest');\n          updateUserRole('guest');\n          setLoading(false);\n        }\n      } catch (error) {\n        console.warn('Auth initialization failed:', error);\n        updateUserRole('guest');\n        setLoading(false);\n      }\n    };\n\n    // Set a timeout to prevent infinite loading\n    const timeoutId = setTimeout(() => {\n      console.warn('Auth initialization timeout, setting as guest');\n      updateUserRole('guest');\n      setLoading(false);\n    }, 5000); // 5 second timeout\n\n    initAuth().finally(() => {\n      clearTimeout(timeoutId);\n    });\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSession(session);\n      setUser(session?.user ?? null);\n      \n      if (session?.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n        updateUserRole('guest');\n        setLoading(false);\n      }\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const fetchProfile = async (userId: string, retryCount = 0) => {\n    try {\n      console.log('Fetching profile for user:', userId, retryCount > 0 ? `(retry ${retryCount})` : '');\n\n      // Add timeout for profile fetch\n      const profilePromise = supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      const timeoutPromise = new Promise((_, reject) =>\n        setTimeout(() => reject(new Error('Profile fetch timeout')), 5000) // Increased timeout\n      );\n\n      const { data, error } = await Promise.race([profilePromise, timeoutPromise]) as any;\n\n      if (error) {\n        console.warn('Profile fetch error:', error);\n\n        // If profile doesn't exist, check for imported profile or create new one\n        if (error.code === 'PGRST116') {\n          console.log('Profile not found, checking for imported profile...');\n          try {\n            const { data: userData } = await supabase.auth.getUser();\n            if (userData.user) {\n              // First, check if there's an imported profile in staging with this email\n              const { data: importedProfile, error: importError } = await supabase\n                .from('imported_profiles_staging')\n                .select('*')\n                .eq('email', userData.user.email!)\n                .single();\n\n              if (importedProfile && !importError) {\n                console.log('Found imported profile, creating linked profile...');\n                // Create profile using imported data\n                const newProfile = {\n                  id: userData.user.id,\n                  email: userData.user.email!,\n                  full_name: userData.user.user_metadata?.full_name || importedProfile.full_name,\n                  role: importedProfile.role as UserRole,\n                  legacy_row_id: importedProfile.legacy_row_id,\n                  pdga_number: importedProfile.pdga_number,\n                  facebook_profile: importedProfile.facebook_profile,\n                  instagram_handle: importedProfile.instagram_handle,\n                  sms_number: importedProfile.sms_number,\n                  phone_number: importedProfile.phone_number,\n                  avatar_url: importedProfile.avatar_url,\n                  created_at: new Date().toISOString(),\n                  updated_at: new Date().toISOString()\n                };\n\n                const { data: createdProfile, error: createError } = await supabase\n                  .from('profiles')\n                  .insert([newProfile])\n                  .select()\n                  .single();\n\n                if (createError) {\n                  console.error('Error creating linked profile:', createError);\n                } else {\n                  console.log('Successfully created linked profile');\n\n                  // Remove from staging since it's now linked\n                  await supabase\n                    .from('imported_profiles_staging')\n                    .delete()\n                    .eq('id', importedProfile.id);\n\n                  setProfile(createdProfile);\n                  updateUserRole(createdProfile.role as UserRole || 'user');\n                  setLoading(false);\n                  return;\n                }\n              }\n\n              // No imported profile found, create a new one\n              console.log('No imported profile found, creating new profile...');\n              const newProfile = {\n                id: userData.user.id,\n                email: userData.user.email!,\n                full_name: userData.user.user_metadata?.full_name || '',\n                role: userData.user.email === '<EMAIL>' ? 'admin' as UserRole : 'user' as UserRole,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n              };\n\n              const { data: createdProfile, error: createError } = await supabase\n                .from('profiles')\n                .insert([newProfile])\n                .select()\n                .single();\n\n              if (createError) {\n                console.error('Error creating new profile:', createError);\n              } else {\n                console.log('Profile created successfully');\n                setProfile(createdProfile);\n                updateUserRole(createdProfile.role as UserRole || 'user');\n                setLoading(false);\n                return;\n              }\n            }\n          } catch (createError) {\n            console.warn('Failed to create profile:', createError);\n          }\n        }\n\n        // If we can't fetch/create profile, preserve existing role if we have one\n        console.log('Continuing without profile data, preserving existing role:', userRole);\n        if (userRole === 'guest') {\n          updateUserRole('user');\n        }\n        // Otherwise keep the existing role to prevent losing admin access\n        setLoading(false);\n        return;\n      }\n\n      console.log('Profile fetched successfully:', data);\n      setProfile(data);\n      setUserRole(data.role || 'user');\n      setLoading(false);\n    } catch (error) {\n      console.error('Error in fetchProfile:', error);\n\n      // Retry on network errors (but not more than 2 times)\n      if (retryCount < 2 && (error as any)?.message?.includes('timeout')) {\n        console.log('Retrying profile fetch due to timeout...');\n        setTimeout(() => fetchProfile(userId, retryCount + 1), 1000 * (retryCount + 1));\n        return;\n      }\n\n      // Don't fail completely, preserve existing role to prevent losing admin access\n      console.log('Preserving existing role due to fetch error:', userRole);\n      if (userRole === 'guest') {\n        setUserRole('user');\n      }\n      // Otherwise keep the existing role\n      setLoading(false);\n    }\n  };\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      });\n\n      // If user creation succeeded, check for imported profile or create new one\n      if (data.user && !error) {\n        try {\n          // Look for an existing imported profile in staging with this email\n          const { data: importedProfile, error: importError } = await supabase\n            .from('imported_profiles_staging')\n            .select('*')\n            .eq('email', data.user.email!)\n            .single();\n\n          if (importedProfile && !importError) {\n            console.log('Found imported profile during signup, creating linked profile...');\n            // Create profile using imported data\n            await supabase\n              .from('profiles')\n              .insert([{\n                id: data.user.id,\n                email: data.user.email!,\n                full_name: fullName || importedProfile.full_name,\n                role: importedProfile.role,\n                legacy_row_id: importedProfile.legacy_row_id,\n                pdga_number: importedProfile.pdga_number,\n                facebook_profile: importedProfile.facebook_profile,\n                instagram_handle: importedProfile.instagram_handle,\n                sms_number: importedProfile.sms_number,\n                phone_number: importedProfile.phone_number,\n                avatar_url: importedProfile.avatar_url,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n              }]);\n\n            // Remove from staging since it's now linked\n            await supabase\n              .from('imported_profiles_staging')\n              .delete()\n              .eq('id', importedProfile.id);\n\n            console.log('Successfully linked imported profile during signup');\n          } else {\n            // No imported profile, create new one\n            await supabase\n              .from('profiles')\n              .insert([{\n                id: data.user.id,\n                email: data.user.email!,\n                full_name: fullName,\n                role: data.user.email === '<EMAIL>' ? 'admin' : 'user',\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n              }]);\n          }\n        } catch (profileError) {\n          console.warn('Profile creation/linking failed, but user was created:', profileError);\n          // Don't fail the signup if profile creation fails\n        }\n      }\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n\n  const updateProfile = async (updates: Partial<Profile>) => {\n    if (!user) return;\n\n    try {\n      const { error } = await supabase\n        .from('profiles')\n        .update(updates)\n        .eq('id', user.id);\n\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    profile,\n    session,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    isGuest: userRole === 'guest',\n    isUser: userRole === 'user',\n    isAdmin: userRole === 'admin',\n    isRakerDiver: userRole === 'rakerdiver',\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE7E,SAASC,QAAQ,QAAiB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBpD,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACrF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAW,MAAM;IACvD;IACA,IAAI;MACF,MAAMqB,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;MAC9D,OAAQF,SAAS,IAAiB,OAAO;IAC3C,CAAC,CAAC,MAAM;MACN,OAAO,OAAO;IAChB;EACF,CAAC,CAAC;EACF,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM0B,cAAc,GAAIC,OAAiB,IAAK;IAC5CP,WAAW,CAACO,OAAO,CAAC;IACpB,IAAI;MACF,IAAIA,OAAO,KAAK,OAAO,EAAE;QACvBL,YAAY,CAACM,UAAU,CAAC,sBAAsB,CAAC;MACjD,CAAC,MAAM;QACLN,YAAY,CAACO,OAAO,CAAC,sBAAsB,EAAEF,OAAO,CAAC;MACvD;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,2CAA2C,EAAEF,KAAK,CAAC;IAClE;EACF,CAAC;EAED/B,SAAS,CAAC,MAAM;IACd;IACA,MAAMkC,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QAAA,IAAAC,aAAA;QACFH,OAAO,CAACI,GAAG,CAAC,sBAAsB,CAAC;QACnC,MAAM;UAAEC,IAAI,EAAE;YAAEnB;UAAQ,CAAC;UAAEa;QAAM,CAAC,GAAG,MAAM7B,QAAQ,CAACoC,IAAI,CAACC,UAAU,CAAC,CAAC;QAErE,IAAIR,KAAK,EAAE;UACTC,OAAO,CAACC,IAAI,CAAC,qBAAqB,EAAEF,KAAK,CAAC;UAC1CJ,cAAc,CAAC,OAAO,CAAC;UACvBD,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEAP,UAAU,CAACD,OAAO,CAAC;QACnBH,OAAO,EAAAoB,aAAA,GAACjB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAqB,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;QAE9B,IAAIjB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;UACjBkB,OAAO,CAACI,GAAG,CAAC,iCAAiC,CAAC;UAC9C,MAAMI,YAAY,CAACtB,OAAO,CAACJ,IAAI,CAAC2B,EAAE,CAAC;QACrC,CAAC,MAAM;UACLT,OAAO,CAACI,GAAG,CAAC,mCAAmC,CAAC;UAChDT,cAAc,CAAC,OAAO,CAAC;UACvBD,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,KAAK,CAAC;QAClDJ,cAAc,CAAC,OAAO,CAAC;QACvBD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACA,MAAMgB,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjCX,OAAO,CAACC,IAAI,CAAC,+CAA+C,CAAC;MAC7DN,cAAc,CAAC,OAAO,CAAC;MACvBD,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEVQ,QAAQ,CAAC,CAAC,CAACU,OAAO,CAAC,MAAM;MACvBC,YAAY,CAACH,SAAS,CAAC;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM;MACJL,IAAI,EAAE;QAAES;MAAa;IACvB,CAAC,GAAG5C,QAAQ,CAACoC,IAAI,CAACS,iBAAiB,CAAC,OAAOC,KAAK,EAAE9B,OAAO,KAAK;MAAA,IAAA+B,cAAA;MAC5D9B,UAAU,CAACD,OAAO,CAAC;MACnBH,OAAO,EAAAkC,cAAA,GAAC/B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAmC,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;MAE9B,IAAI/B,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;QACjB,MAAM0B,YAAY,CAACtB,OAAO,CAACJ,IAAI,CAAC2B,EAAE,CAAC;MACrC,CAAC,MAAM;QACLxB,UAAU,CAAC,IAAI,CAAC;QAChBU,cAAc,CAAC,OAAO,CAAC;QACvBD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IAEF,OAAO,MAAMoB,YAAY,CAACI,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMV,YAAY,GAAG,MAAAA,CAAOW,MAAc,EAAEC,UAAU,GAAG,CAAC,KAAK;IAC7D,IAAI;MACFpB,OAAO,CAACI,GAAG,CAAC,4BAA4B,EAAEe,MAAM,EAAEC,UAAU,GAAG,CAAC,GAAG,UAAUA,UAAU,GAAG,GAAG,EAAE,CAAC;;MAEhG;MACA,MAAMC,cAAc,GAAGnD,QAAQ,CAC5BoD,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEL,MAAM,CAAC,CAChBM,MAAM,CAAC,CAAC;MAEX,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAC3ClB,UAAU,CAAC,MAAMkB,MAAM,CAAC,IAAInD,KAAK,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;MACrE,CAAC;MAED,MAAM;QAAE2B,IAAI;QAAEN;MAAM,CAAC,GAAG,MAAM4B,OAAO,CAACG,IAAI,CAAC,CAACT,cAAc,EAAEK,cAAc,CAAC,CAAQ;MAEnF,IAAI3B,KAAK,EAAE;QACTC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAEF,KAAK,CAAC;;QAE3C;QACA,IAAIA,KAAK,CAACgC,IAAI,KAAK,UAAU,EAAE;UAC7B/B,OAAO,CAACI,GAAG,CAAC,qDAAqD,CAAC;UAClE,IAAI;YACF,MAAM;cAAEC,IAAI,EAAE2B;YAAS,CAAC,GAAG,MAAM9D,QAAQ,CAACoC,IAAI,CAAC2B,OAAO,CAAC,CAAC;YACxD,IAAID,QAAQ,CAAClD,IAAI,EAAE;cAAA,IAAAoD,sBAAA;cACjB;cACA,MAAM;gBAAE7B,IAAI,EAAE8B,eAAe;gBAAEpC,KAAK,EAAEqC;cAAY,CAAC,GAAG,MAAMlE,QAAQ,CACjEoD,IAAI,CAAC,2BAA2B,CAAC,CACjCC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,OAAO,EAAEQ,QAAQ,CAAClD,IAAI,CAACuD,KAAM,CAAC,CACjCZ,MAAM,CAAC,CAAC;cAEX,IAAIU,eAAe,IAAI,CAACC,WAAW,EAAE;gBAAA,IAAAE,qBAAA;gBACnCtC,OAAO,CAACI,GAAG,CAAC,oDAAoD,CAAC;gBACjE;gBACA,MAAMmC,UAAU,GAAG;kBACjB9B,EAAE,EAAEuB,QAAQ,CAAClD,IAAI,CAAC2B,EAAE;kBACpB4B,KAAK,EAAEL,QAAQ,CAAClD,IAAI,CAACuD,KAAM;kBAC3BG,SAAS,EAAE,EAAAF,qBAAA,GAAAN,QAAQ,CAAClD,IAAI,CAAC2D,aAAa,cAAAH,qBAAA,uBAA3BA,qBAAA,CAA6BE,SAAS,KAAIL,eAAe,CAACK,SAAS;kBAC9EE,IAAI,EAAEP,eAAe,CAACO,IAAgB;kBACtCC,aAAa,EAAER,eAAe,CAACQ,aAAa;kBAC5CC,WAAW,EAAET,eAAe,CAACS,WAAW;kBACxCC,gBAAgB,EAAEV,eAAe,CAACU,gBAAgB;kBAClDC,gBAAgB,EAAEX,eAAe,CAACW,gBAAgB;kBAClDC,UAAU,EAAEZ,eAAe,CAACY,UAAU;kBACtCC,YAAY,EAAEb,eAAe,CAACa,YAAY;kBAC1CC,UAAU,EAAEd,eAAe,CAACc,UAAU;kBACtCC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;kBACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;gBACrC,CAAC;gBAED,MAAM;kBAAE/C,IAAI,EAAEiD,cAAc;kBAAEvD,KAAK,EAAEwD;gBAAY,CAAC,GAAG,MAAMrF,QAAQ,CAChEoD,IAAI,CAAC,UAAU,CAAC,CAChBkC,MAAM,CAAC,CAACjB,UAAU,CAAC,CAAC,CACpBhB,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;gBAEX,IAAI8B,WAAW,EAAE;kBACfvD,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEwD,WAAW,CAAC;gBAC9D,CAAC,MAAM;kBACLvD,OAAO,CAACI,GAAG,CAAC,qCAAqC,CAAC;;kBAElD;kBACA,MAAMlC,QAAQ,CACXoD,IAAI,CAAC,2BAA2B,CAAC,CACjCmC,MAAM,CAAC,CAAC,CACRjC,EAAE,CAAC,IAAI,EAAEW,eAAe,CAAC1B,EAAE,CAAC;kBAE/BxB,UAAU,CAACqE,cAAc,CAAC;kBAC1B3D,cAAc,CAAC2D,cAAc,CAACZ,IAAI,IAAgB,MAAM,CAAC;kBACzDhD,UAAU,CAAC,KAAK,CAAC;kBACjB;gBACF;cACF;;cAEA;cACAM,OAAO,CAACI,GAAG,CAAC,oDAAoD,CAAC;cACjE,MAAMmC,UAAU,GAAG;gBACjB9B,EAAE,EAAEuB,QAAQ,CAAClD,IAAI,CAAC2B,EAAE;gBACpB4B,KAAK,EAAEL,QAAQ,CAAClD,IAAI,CAACuD,KAAM;gBAC3BG,SAAS,EAAE,EAAAN,sBAAA,GAAAF,QAAQ,CAAClD,IAAI,CAAC2D,aAAa,cAAAP,sBAAA,uBAA3BA,sBAAA,CAA6BM,SAAS,KAAI,EAAE;gBACvDE,IAAI,EAAEV,QAAQ,CAAClD,IAAI,CAACuD,KAAK,KAAK,mCAAmC,GAAG,OAAO,GAAe,MAAkB;gBAC5Ga,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;gBACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;cACrC,CAAC;cAED,MAAM;gBAAE/C,IAAI,EAAEiD,cAAc;gBAAEvD,KAAK,EAAEwD;cAAY,CAAC,GAAG,MAAMrF,QAAQ,CAChEoD,IAAI,CAAC,UAAU,CAAC,CAChBkC,MAAM,CAAC,CAACjB,UAAU,CAAC,CAAC,CACpBhB,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;cAEX,IAAI8B,WAAW,EAAE;gBACfvD,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEwD,WAAW,CAAC;cAC3D,CAAC,MAAM;gBACLvD,OAAO,CAACI,GAAG,CAAC,8BAA8B,CAAC;gBAC3CnB,UAAU,CAACqE,cAAc,CAAC;gBAC1B3D,cAAc,CAAC2D,cAAc,CAACZ,IAAI,IAAgB,MAAM,CAAC;gBACzDhD,UAAU,CAAC,KAAK,CAAC;gBACjB;cACF;YACF;UACF,CAAC,CAAC,OAAO6D,WAAW,EAAE;YACpBvD,OAAO,CAACC,IAAI,CAAC,2BAA2B,EAAEsD,WAAW,CAAC;UACxD;QACF;;QAEA;QACAvD,OAAO,CAACI,GAAG,CAAC,4DAA4D,EAAEhB,QAAQ,CAAC;QACnF,IAAIA,QAAQ,KAAK,OAAO,EAAE;UACxBO,cAAc,CAAC,MAAM,CAAC;QACxB;QACA;QACAD,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAM,OAAO,CAACI,GAAG,CAAC,+BAA+B,EAAEC,IAAI,CAAC;MAClDpB,UAAU,CAACoB,IAAI,CAAC;MAChBhB,WAAW,CAACgB,IAAI,CAACqC,IAAI,IAAI,MAAM,CAAC;MAChChD,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAA2D,QAAA;MACd1D,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,IAAIqB,UAAU,GAAG,CAAC,IAAKrB,KAAK,aAALA,KAAK,gBAAA2D,QAAA,GAAL3D,KAAK,CAAU4D,OAAO,cAAAD,QAAA,eAAvBA,QAAA,CAAyBE,QAAQ,CAAC,SAAS,CAAC,EAAE;QAClE5D,OAAO,CAACI,GAAG,CAAC,0CAA0C,CAAC;QACvDO,UAAU,CAAC,MAAMH,YAAY,CAACW,MAAM,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,IAAIA,UAAU,GAAG,CAAC,CAAC,CAAC;QAC/E;MACF;;MAEA;MACApB,OAAO,CAACI,GAAG,CAAC,8CAA8C,EAAEhB,QAAQ,CAAC;MACrE,IAAIA,QAAQ,KAAK,OAAO,EAAE;QACxBC,WAAW,CAAC,MAAM,CAAC;MACrB;MACA;MACAK,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmE,MAAM,GAAG,MAAAA,CAAOxB,KAAa,EAAEyB,QAAgB,EAAEC,QAAgB,KAAK;IAC1E,IAAI;MACF,MAAM;QAAE1D,IAAI;QAAEN;MAAM,CAAC,GAAG,MAAM7B,QAAQ,CAACoC,IAAI,CAACuD,MAAM,CAAC;QACjDxB,KAAK;QACLyB,QAAQ;QACRE,OAAO,EAAE;UACP3D,IAAI,EAAE;YACJmC,SAAS,EAAEuB;UACb;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAI1D,IAAI,CAACvB,IAAI,IAAI,CAACiB,KAAK,EAAE;QACvB,IAAI;UACF;UACA,MAAM;YAAEM,IAAI,EAAE8B,eAAe;YAAEpC,KAAK,EAAEqC;UAAY,CAAC,GAAG,MAAMlE,QAAQ,CACjEoD,IAAI,CAAC,2BAA2B,CAAC,CACjCC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,OAAO,EAAEnB,IAAI,CAACvB,IAAI,CAACuD,KAAM,CAAC,CAC7BZ,MAAM,CAAC,CAAC;UAEX,IAAIU,eAAe,IAAI,CAACC,WAAW,EAAE;YACnCpC,OAAO,CAACI,GAAG,CAAC,kEAAkE,CAAC;YAC/E;YACA,MAAMlC,QAAQ,CACXoD,IAAI,CAAC,UAAU,CAAC,CAChBkC,MAAM,CAAC,CAAC;cACP/C,EAAE,EAAEJ,IAAI,CAACvB,IAAI,CAAC2B,EAAE;cAChB4B,KAAK,EAAEhC,IAAI,CAACvB,IAAI,CAACuD,KAAM;cACvBG,SAAS,EAAEuB,QAAQ,IAAI5B,eAAe,CAACK,SAAS;cAChDE,IAAI,EAAEP,eAAe,CAACO,IAAI;cAC1BC,aAAa,EAAER,eAAe,CAACQ,aAAa;cAC5CC,WAAW,EAAET,eAAe,CAACS,WAAW;cACxCC,gBAAgB,EAAEV,eAAe,CAACU,gBAAgB;cAClDC,gBAAgB,EAAEX,eAAe,CAACW,gBAAgB;cAClDC,UAAU,EAAEZ,eAAe,CAACY,UAAU;cACtCC,YAAY,EAAEb,eAAe,CAACa,YAAY;cAC1CC,UAAU,EAAEd,eAAe,CAACc,UAAU;cACtCC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;cACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YACrC,CAAC,CAAC,CAAC;;YAEL;YACA,MAAMlF,QAAQ,CACXoD,IAAI,CAAC,2BAA2B,CAAC,CACjCmC,MAAM,CAAC,CAAC,CACRjC,EAAE,CAAC,IAAI,EAAEW,eAAe,CAAC1B,EAAE,CAAC;YAE/BT,OAAO,CAACI,GAAG,CAAC,oDAAoD,CAAC;UACnE,CAAC,MAAM;YACL;YACA,MAAMlC,QAAQ,CACXoD,IAAI,CAAC,UAAU,CAAC,CAChBkC,MAAM,CAAC,CAAC;cACP/C,EAAE,EAAEJ,IAAI,CAACvB,IAAI,CAAC2B,EAAE;cAChB4B,KAAK,EAAEhC,IAAI,CAACvB,IAAI,CAACuD,KAAM;cACvBG,SAAS,EAAEuB,QAAQ;cACnBrB,IAAI,EAAErC,IAAI,CAACvB,IAAI,CAACuD,KAAK,KAAK,mCAAmC,GAAG,OAAO,GAAG,MAAM;cAChFa,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;cACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YACrC,CAAC,CAAC,CAAC;UACP;QACF,CAAC,CAAC,OAAOa,YAAY,EAAE;UACrBjE,OAAO,CAACC,IAAI,CAAC,wDAAwD,EAAEgE,YAAY,CAAC;UACpF;QACF;MACF;MAEA,OAAO;QAAE5D,IAAI;QAAEN;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEM,IAAI,EAAE,IAAI;QAAEN,KAAK,EAAE;UAAE4D,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMO,MAAM,GAAG,MAAAA,CAAO7B,KAAa,EAAEyB,QAAgB,KAAK;IACxD,IAAI;MACF,MAAM;QAAEzD,IAAI;QAAEN;MAAM,CAAC,GAAG,MAAM7B,QAAQ,CAACoC,IAAI,CAAC6D,kBAAkB,CAAC;QAC7D9B,KAAK;QACLyB;MACF,CAAC,CAAC;MAEF,OAAO;QAAEzD,IAAI;QAAEN;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEM,IAAI,EAAE,IAAI;QAAEN,KAAK,EAAE;UAAE4D,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMS,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAM;QAAErE;MAAM,CAAC,GAAG,MAAM7B,QAAQ,CAACoC,IAAI,CAAC8D,OAAO,CAAC,CAAC;MAC/C,IAAIrE,KAAK,EAAE;QACTC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACI,GAAG,CAAC,+CAA+C,CAAC;IAC9D;EACF,CAAC;EAED,MAAMiE,aAAa,GAAG,MAAOC,OAAyB,IAAK;IACzD,IAAI,CAACxF,IAAI,EAAE;IAEX,IAAI;MACF,MAAM;QAAEiB;MAAM,CAAC,GAAG,MAAM7B,QAAQ,CAC7BoD,IAAI,CAAC,UAAU,CAAC,CAChBiD,MAAM,CAACD,OAAO,CAAC,CACf9C,EAAE,CAAC,IAAI,EAAE1C,IAAI,CAAC2B,EAAE,CAAC;MAEpB,IAAIV,KAAK,EAAE;QACT,MAAMA,KAAK;MACb;;MAEA;MACA,MAAMS,YAAY,CAAC1B,IAAI,CAAC2B,EAAE,CAAC;IAC7B,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMyE,KAAK,GAAG;IACZ1F,IAAI;IACJE,OAAO;IACPE,OAAO;IACPE,QAAQ;IACRK,OAAO;IACPoE,MAAM;IACNK,MAAM;IACNE,OAAO;IACPC,aAAa;IACbI,OAAO,EAAErF,QAAQ,KAAK,OAAO;IAC7BsF,MAAM,EAAEtF,QAAQ,KAAK,MAAM;IAC3BuF,OAAO,EAAEvF,QAAQ,KAAK,OAAO;IAC7BwF,YAAY,EAAExF,QAAQ,KAAK;EAC7B,CAAC;EAED,oBACEhB,OAAA,CAACC,WAAW,CAACwG,QAAQ;IAACL,KAAK,EAAEA,KAAM;IAAA5F,QAAA,EAChCA;EAAQ;IAAAkG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACpG,GAAA,CAtXWF,YAAqD;AAAAuG,EAAA,GAArDvG,YAAqD;AAAA,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}